#include "NWCNP_Graph.h"
#include <algorithm>

NWCNP_Graph::NWCNP_Graph(NodeSet nodes,
                         std::unordered_map<int, double> nodeWeights,
                         std::vector<NodeSet> adjList,
                         double bound,
                         int seed)
{
    numNodes = nodes.size();
    originalNodesSet = nodes;
    this->nodeWeights = nodeWeights;
    nodeAge.resize(numNodes, 0);
    currentAdjList = adjList;
    originalAdjList = adjList;
    this->bound = bound;
    SolWeight = 0;
    nodeToComponentIndex.resize(numNodes, -1);
    rng.setSeed(seed);
}

void NWCNP_Graph::initializeComponentsAndMapping()
{

    std::fill(nodeToComponentIndex.begin(), nodeToComponentIndex.end(), -1);

    connectedComponents.clear();
    connectedPairs = 0;

    ComponentIndex componentIndex = 0;
    std::vector<bool> visited(numNodes, false);

    for (Node node : originalNodesSet)
    {
        if (!visited[node] && !isNodeRemoved(node))
        {

            Component component = DFSfindComponent(node);
            if (!component.nodes.empty())
            {
                connectedComponents.push_back(component);

                for (Node componentNode : component.nodes)
                {
                    visited[componentNode] = true;
                    nodeToComponentIndex[componentNode] = componentIndex;
                }
                componentIndex++;

                connectedPairs += (component.size * (component.size - 1)) / 2;
            }
        }
    }
}

Component NWCNP_Graph::DFSfindComponent(Node startNode) const
{
    Component newComponent;
    newComponent.nodes.reserve(numNodes);

    std::vector<bool> isVisited(numNodes, false);
    std::vector<Node> stack;
    stack.reserve(numNodes);
    stack.push_back(startNode);

    while (!stack.empty())
    {
        Node node = stack.back();
        stack.pop_back();

        if (isVisited[node] || isNodeRemoved(node))
        {
            continue;
        }

        isVisited[node] = true;
        newComponent.nodes.push_back(node);

        const auto &neighbors = currentAdjList[node];
        for (const Node &neighbor : neighbors)
        {
            if (!isVisited[neighbor] && !isNodeRemoved(neighbor))
            {
                stack.push_back(neighbor);
            }
        }
    }

    newComponent.size = newComponent.nodes.size();
    return newComponent;
}

void NWCNP_Graph::updateGraphByRemovedNodes(const NodeSet &nodesToRemove)
{

    removedNodes.clear();
    SolWeight = 0;

    currentAdjList = originalAdjList;

    for (Node node : nodesToRemove)
    {
        removedNodes.insert(node);
        SolWeight += nodeWeights.at(node);
    }

    for (Node node : nodesToRemove)
    {

        for (Node neighbor : originalAdjList[node])
        {
            currentAdjList[neighbor].erase(node);
        }

        currentAdjList[node].clear();
    }

    initializeComponentsAndMapping();
}

void NWCNP_Graph::getReducedGraphByRemovedNodes(const NodeSet &removeSet)
{

    removedNodes.clear();
    SolWeight = 0;

    for (Node node : removeSet)
    {
        bound -= nodeWeights.at(node);
    }

    for (Node node : removeSet)
    {

        originalNodesSet.erase(node);

        for (Node neighbor : originalAdjList[node])
        {
            originalAdjList[neighbor].erase(node);
        }

        originalAdjList[node].clear();
    }

    currentAdjList = originalAdjList;

    initializeComponentsAndMapping();
}

void NWCNP_Graph::addNode(Node nodeToAdd)
{

    removedNodes.erase(nodeToAdd);

    SolWeight -= nodeWeights.at(nodeToAdd);

    ComponentIndex componentIndex = -1;
    for (Node neighbor : originalAdjList[nodeToAdd])
    {
        if (nodeToComponentIndex[neighbor] != -1)
        {
            currentAdjList[nodeToAdd].insert(neighbor);
            currentAdjList[neighbor].insert(nodeToAdd);
            if (componentIndex == -1)
            {
                componentIndex = nodeToComponentIndex[neighbor];
            }
        }
    }

    if (componentIndex != -1)
    {
        connectedComponents[componentIndex].nodes.push_back(nodeToAdd);

        connectedComponents[componentIndex].size++;
        nodeToComponentIndex[nodeToAdd] = componentIndex;
        Component newComponent = DFSfindComponent(nodeToAdd);

        if (newComponent.size > connectedComponents[componentIndex].size)
        {

            connectedComponents[componentIndex].size--;

            std::set<ComponentIndex> componentsIndexesToMerge;
            for (Node node : newComponent.nodes)
            {
                if (nodeToComponentIndex[node] != -1)
                {
                    componentsIndexesToMerge.insert(nodeToComponentIndex[node]);
                }
            }

            std::vector<int> indexMapping(connectedComponents.size());

            std::iota(indexMapping.begin(), indexMapping.end(), 0);

            for (ComponentIndex idx : componentsIndexesToMerge)
            {
                for (size_t i = idx + 1; i < indexMapping.size(); ++i)
                {
                    indexMapping[i]--;
                }
            }

            for (Node node : originalNodesSet)
            {
                if (nodeToComponentIndex[node] != -1)
                {
                    nodeToComponentIndex[node]
                        = indexMapping[nodeToComponentIndex[node]];
                }
            }

            for (auto it = componentsIndexesToMerge.rbegin();
                 it != componentsIndexesToMerge.rend();
                 ++it)
            {
                connectedPairs -= connectedComponents[*it].size
                                  * (connectedComponents[*it].size - 1) / 2;
                connectedComponents.erase(connectedComponents.begin() + *it);
            }

            connectedComponents.push_back(newComponent);
            connectedPairs += newComponent.size * (newComponent.size - 1) / 2;
            for (Node node : newComponent.nodes)
            {
                nodeToComponentIndex[node] = connectedComponents.size() - 1;
            }
        }

        else if (newComponent.size == connectedComponents[componentIndex].size)
        {
            connectedPairs += (connectedComponents[componentIndex].size - 1);
        }
    }
    else
    {

        Component newComponent;
        newComponent.nodes.push_back(nodeToAdd);
        newComponent.size = 1;
        connectedComponents.push_back(newComponent);

        nodeToComponentIndex[nodeToAdd] = connectedComponents.size() - 1;
    }
}

void NWCNP_Graph::removeNode(Node nodeToRemove)

{
    ComponentIndex componentIndex = nodeToComponentIndex[nodeToRemove];
    Component originalComponent = connectedComponents[componentIndex];

    removedNodes.insert(nodeToRemove);

    SolWeight += nodeWeights.at(nodeToRemove);

    nodeToComponentIndex[nodeToRemove] = -1;

    const auto &neighbors = currentAdjList[nodeToRemove];
    for (Node neighbor : neighbors)
    {
        currentAdjList[neighbor].erase(nodeToRemove);
    }
    currentAdjList[nodeToRemove].clear();

    if (originalComponent.size == 1)
    {

        for (size_t i = componentIndex + 1; i < connectedComponents.size(); i++)
        {
            for (Node node : connectedComponents[i].nodes)
            {
                nodeToComponentIndex[node]--;
            }
        }
        connectedComponents.erase(connectedComponents.begin() + componentIndex);
    }

    if (originalComponent.size > 1)
    {

        connectedComponents[componentIndex].nodes.erase(
            std::remove(connectedComponents[componentIndex].nodes.begin(),
                       connectedComponents[componentIndex].nodes.end(),
                       nodeToRemove),
            connectedComponents[componentIndex].nodes.end());
        connectedComponents[componentIndex].size--;

        std::vector<bool> isVisited(numNodes);
        Node startNode;
        for (Node node : originalComponent.nodes)
        {
            if (node != nodeToRemove)
            {
                startNode = node;
                break;
            }
        }

        Component newComponent = DFSfindComponent(startNode);

        if (newComponent.size < connectedComponents[componentIndex].size)
        {

            connectedPairs -= connectedComponents[componentIndex].size
                              * (connectedComponents[componentIndex].size + 1)
                              / 2;

            connectedComponents[componentIndex] = newComponent;
            connectedPairs += newComponent.size * (newComponent.size - 1) / 2;

            for (Node node : newComponent.nodes)
            {
                isVisited[node] = true;
                nodeToComponentIndex[node] = componentIndex;
            }

            for (Node node : originalComponent.nodes)
            {

                if (isVisited[node] || node == nodeToRemove)
                    continue;

                Component splitComponent = DFSfindComponent(node);
                ComponentIndex newIndex = connectedComponents.size();
                connectedComponents.push_back(splitComponent);
                connectedPairs
                    += splitComponent.size * (splitComponent.size - 1) / 2;

                for (Node node : splitComponent.nodes)
                {
                    nodeToComponentIndex[node] = newIndex;
                    isVisited[node] = true;
                }
            }
        }
        else
        {

            connectedPairs -= newComponent.size;
        }
    }
}

ComponentIndex NWCNP_Graph::selectRemovedComponent() const
{
    int numComponents = connectedComponents.size();
    std::vector<ComponentIndex> largeComponents;
    largeComponents.reserve(numComponents);

    if (numComponents > 50)
    {
        return selectRemovedLargerComponent();
    }

    int minSize = numNodes;
    int maxSize = 0;

    for (size_t i = 0; i < static_cast<size_t>(numComponents); ++i)
    {
        const size_t size = connectedComponents[i].size;
        if (size > 2)
        {
            minSize = std::min(minSize, static_cast<int>(size));
            maxSize = std::max(maxSize, static_cast<int>(size));
        }
    }

    const double sizeThreshold
        = maxSize - (maxSize - minSize) * 0.5 - rng.generateIndex(3);

    for (size_t i = 0; i < static_cast<size_t>(numComponents); ++i)
    {
        if (connectedComponents[i].size >= sizeThreshold)
        {
            largeComponents.push_back(i);
        }
    }

    if (largeComponents.empty())
    {
        throw std::runtime_error("无法找到合适的大连通分量");
    }

    return largeComponents[rng.generateIndex(largeComponents.size())];
}

ComponentIndex NWCNP_Graph::selectRemovedLargerComponent() const
{

    int totalSize = numNodes - removedNodes.size();
    int numComponents = connectedComponents.size();
    int avgComponentSize = std::max(
        2,
        static_cast<int>(std::round(static_cast<float>(totalSize)
                                    / static_cast<float>(numComponents))));

    std::vector<ComponentIndex> largeComponents;
    std::vector<size_t> componentSizes;
    largeComponents.reserve(numComponents);
    componentSizes.reserve(numComponents);

    int totalNodesInBigComponents = 0;
    int maxSize = 0;
    int maxIndex = 0;
    int secondMaxSize = 0;
    int secondMaxIndex = 0;

    for (size_t i = 0; i < static_cast<size_t>(numComponents); ++i)
    {
        const size_t currentSize = connectedComponents[i].size;

        if (static_cast<int>(currentSize) > avgComponentSize)
        {
            largeComponents.push_back(i);
            componentSizes.push_back(currentSize);
            totalNodesInBigComponents += currentSize;

            if (static_cast<int>(currentSize) > maxSize)
            {
                secondMaxSize = maxSize;
                secondMaxIndex = maxIndex;
                maxSize = static_cast<int>(currentSize);
                maxIndex = i;
            }
            else if (static_cast<int>(currentSize) > secondMaxSize)
            {
                secondMaxSize = static_cast<int>(currentSize);
                secondMaxIndex = i;
            }
        }
    }

    if (largeComponents.size() == 1)
    {

        return rng.generateBool(0.5) ? secondMaxIndex : largeComponents[0];
    }

    const int index = rng.generateIndex(totalNodesInBigComponents);
    int sum = 0;

    for (size_t i = 0; i < largeComponents.size(); ++i)
    {
        sum += componentSizes[i];
        if (index < sum)
        {
            return largeComponents[i];
        }
    }

    return largeComponents.back();
}

Node NWCNP_Graph::randomSelectNodeFromComponent(
    ComponentIndex componentIndex) const
{
    const auto &component = connectedComponents[componentIndex];
    if (component.nodes.empty())
    {
        throw std::runtime_error("component is empty, can not select node");
    }

    return component.nodes[rng.generateIndex(component.size)];
}

Node NWCNP_Graph::ageSelectNodeFromComponent(
    ComponentIndex componentIndex) const
{
    const auto &component = connectedComponents[componentIndex];
    if (component.size == 0)
    {
        throw std::runtime_error("component is empty, can not select node");
    }

    std::vector<Node> candidateNodes;
    candidateNodes.reserve(component.size);

    const Node firstNode = component.nodes[0];
    int minAge = nodeAge[firstNode];
    candidateNodes.push_back(firstNode);

    for (size_t i = 1; i < component.size; ++i)
    {
        Node currentNode = component.nodes[i];
        if (nodeAge[currentNode] < minAge)
        {

            minAge = nodeAge[currentNode];
            candidateNodes.clear();
            candidateNodes.push_back(currentNode);
        }
        else if (nodeAge[currentNode] == minAge)
        {

            candidateNodes.push_back(currentNode);
        }
    }

    return candidateNodes.size() == 1
               ? candidateNodes[0]
               : candidateNodes[rng.generateIndex(candidateNodes.size())];
}

Node NWCNP_Graph::impactSelectNodeFromComponent(
    ComponentIndex componentIndex) const
{
    const auto &component = connectedComponents[componentIndex];
    if (component.size == 0)
    {
        throw std::runtime_error("component is empty, can not select node");
    }

    std::vector<int> nodeToIdx(numNodes, -1);
    for (size_t i = 0; i < component.size; ++i)
    {
        nodeToIdx[component.nodes[i]] = i;
    }

    const int n = component.size;
    dfn.assign(n + 1, 0);
    lowVec.assign(n + 1, 0);
    stSizeVec.assign(n + 1, 1);
    cutSizeVec.assign(n + 1, 1);
    impactVec.assign(n + 1, 0.0);
    flagVec.assign(n + 1, 0);
    isCutVec.assign(n + 1, false);

    std::vector<Node> candidateNodes;
    candidateNodes.reserve(n);

    timeStamp = 0;
    nodeRoot = 1;

    tarjanInComponent(componentIndex, nodeRoot, nodeToIdx);

    double minImpact = std::numeric_limits<double>::max();
    const double EPSILON = 1e-6;

    for (int i = 1; i <= n; ++i)
    {

        if (isCutVec[i])
        {

            impactVec[i] += ((timeStamp - cutSizeVec[i])
                             * (timeStamp - cutSizeVec[i] - 1))
                            / 2;
        }
        else
        {

            impactVec[i] += ((timeStamp - 1) * (timeStamp - 2)) / 2;
        }

        impactVec[i] += numNodes * nodeWeights.at(component.nodes[i - 1]);

        if (impactVec[i] < minImpact - EPSILON)
        {
            minImpact = impactVec[i];
            candidateNodes.clear();
            candidateNodes.push_back(component.nodes[i - 1]);
        }
        else if (std::abs(impactVec[i] - minImpact) < EPSILON)
        {
            candidateNodes.push_back(component.nodes[i - 1]);
        }
    }

    return candidateNodes.size() == 1
               ? candidateNodes[0]
               : candidateNodes[rng.generateIndex(candidateNodes.size())];
}

void NWCNP_Graph::tarjanInComponent(ComponentIndex compIndex,
                                    int nodeIdx,
                                    const std::vector<int> &nodeToIdx) const
{

    dfn[nodeIdx] = lowVec[nodeIdx] = ++timeStamp;

    Node nodeId = connectedComponents[compIndex].nodes[nodeIdx - 1];

    for (Node neighbor : currentAdjList[nodeId])
    {

        if (!isNodeRemoved(neighbor)
            && nodeToComponentIndex[neighbor] == compIndex)
        {

            int neighborIdx = nodeToIdx[neighbor] + 1;

            if (dfn[neighborIdx] == 0)
            {

                tarjanInComponent(compIndex, neighborIdx, nodeToIdx);

                lowVec[nodeIdx]
                    = std::min(lowVec[nodeIdx], lowVec[neighborIdx]);

                if (dfn[nodeIdx] < dfn[neighborIdx])
                {
                    stSizeVec[nodeIdx] += stSizeVec[neighborIdx];
                }

                if (lowVec[neighborIdx] >= dfn[nodeIdx])
                {
                    flagVec[nodeIdx]++;

                    if (nodeIdx != nodeRoot)
                    {
                        isCutVec[nodeIdx] = true;
                        cutSizeVec[nodeIdx] += stSizeVec[neighborIdx];
                        impactVec[nodeIdx] += (stSizeVec[neighborIdx]
                                               * (stSizeVec[neighborIdx] - 1))
                                              / 2;
                    }

                    else if (nodeIdx == nodeRoot && flagVec[nodeIdx] > 1)
                    {
                        isCutVec[nodeIdx] = true;
                    }
                }
            }

            else
            {
                lowVec[nodeIdx] = std::min(lowVec[nodeIdx], dfn[neighborIdx]);
            }
        }
    }
}

Node NWCNP_Graph::greedySelectNodeToAdd() const
{
    if (removedNodes.empty())
    {
        throw std::runtime_error("no removed nodes can be added");
    }

    std::vector<Node> candidateNodes;
    candidateNodes.reserve(removedNodes.size());

    auto it = removedNodes.begin();
    const Node firstNode = *it;
    int minDelta = calculateConnectionGain(firstNode);
    candidateNodes.push_back(firstNode);

    ++it;
    for (; it != removedNodes.end(); ++it)
    {
        Node currentNode = *it;
        int connectionGain = calculateConnectionGain(currentNode);

        if (connectionGain < minDelta)
        {

            minDelta = connectionGain;
            candidateNodes.clear();
            candidateNodes.push_back(currentNode);
        }
        else if (connectionGain == minDelta)
        {

            candidateNodes.push_back(currentNode);
        }
    }

    return candidateNodes.size() == 1
               ? candidateNodes[0]
               : candidateNodes[rng.generateIndex(candidateNodes.size())];
}

Node NWCNP_Graph::randomSelectNodeToRemove() const
{

    ComponentIndex compIndex = rng.generateIndex(connectedComponents.size());
    const Component &selectedComponent = connectedComponents[compIndex];

    return selectedComponent.nodes[rng.generateIndex(selectedComponent.size)];
}

int NWCNP_Graph::calculateConnectionGain(Node node) const
{

    std::vector<size_t> componentSizes(connectedComponents.size(), 0);

    int totalSize = 1;

    const auto &neighbors = originalAdjList[node];
    for (Node neighbor : neighbors)
    {

        if (nodeToComponentIndex[neighbor] != -1)
        {
            ComponentIndex compIndex = nodeToComponentIndex[neighbor];

            if (componentSizes[compIndex] == 0)
            {
                componentSizes[compIndex] = connectedComponents[compIndex].size;
                totalSize += componentSizes[compIndex];
            }
        }
    }

    int oldConnectionsSum = 0;
    for (int size : componentSizes)
    {
        if (size > 0)
        {
            oldConnectionsSum += (size * (size - 1)) / 2;
        }
    }

    int newConnections = (totalSize * (totalSize - 1)) / 2;

    return newConnections - oldConnectionsSum;
}

std::unique_ptr<Graph> NWCNP_Graph::getRandomFeasibleGraph()
{
    auto tempGraph = std::make_unique<NWCNP_Graph>(*this);

    Solution randomSolution;
    std::vector<Node> availableNodes(originalNodesSet.begin(),
                                     originalNodesSet.end());
    double solutionWeight = 0;

    while (!availableNodes.empty())
    {
        int randomIndex = rng.generateIndex(availableNodes.size());
        Node node = availableNodes[randomIndex];

        if (solutionWeight + nodeWeights.at(node) > bound)
        {
            break;
        }

        randomSolution.insert(node);
        solutionWeight += nodeWeights.at(node);

        availableNodes[randomIndex] = availableNodes.back();
        availableNodes.pop_back();
    }

    tempGraph->updateGraphByRemovedNodes(randomSolution);

    return tempGraph;
}

std::unique_ptr<Graph> NWCNP_Graph::clone() const
{
    return std::make_unique<NWCNP_Graph>(*this);
}
